import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import type { Metadata } from 'next';
import HtmlLangSetter from '@/components/HtmlLangSetter';
import { generatePageMetadata } from '@/lib/metadata';

// Generate locale-specific metadata
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;

  // For homepage, use the home page metadata
  return generatePageMetadata('home', locale, locale === 'bs' ? '/' : `/${locale}`);
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  await params; // Ensure params are awaited for Next.js
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <HtmlLangSetter />
      {children}
    </NextIntlClientProvider>
  );
}
