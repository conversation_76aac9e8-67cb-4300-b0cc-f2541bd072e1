import type { <PERSON>ada<PERSON> } from "next";
import localFont from "next/font/local";
import "./[locale]/globals.css";

const metroSans = localFont({
  src: [
    // Light weights
    {
      path: '../../public/fonts/metro-sans/MetroSans-Light.ttf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../public/fonts/metro-sans/MetroSans-LightItalic.ttf',
      weight: '300',
      style: 'italic',
    },
    // Book weights (normal)
    {
      path: '../../public/fonts/metro-sans/MetroSans-Book.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/metro-sans/MetroSans-BookItalic.ttf',
      weight: '400',
      style: 'italic',
    },
    // Regular weights (also normal, but different variant)
    {
      path: '../../public/fonts/metro-sans/MetroSans-Regular.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/metro-sans/MetroSans-RegularItalic.ttf',
      weight: '400',
      style: 'italic',
    },
    // Medium weights
    {
      path: '../../public/fonts/metro-sans/MetroSans-Medium.ttf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/metro-sans/MetroSans-MediumItalic.ttf',
      weight: '500',
      style: 'italic',
    },
    // Semi-bold weights
    {
      path: '../../public/fonts/metro-sans/MetroSans-SemiBold.ttf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../public/fonts/metro-sans/MetroSans-SemiBoldItalic.ttf',
      weight: '600',
      style: 'italic',
    },
    // Bold weights
    {
      path: '../../public/fonts/metro-sans/MetroSans-Bold.ttf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../public/fonts/metro-sans/MetroSans-BoldItalic.ttf',
      weight: '700',
      style: 'italic',
    },
  ],
  variable: '--font-metro-sans',
  display: 'swap',
});

export const metadata: Metadata = {
  title: "AlbatrosDoc",
  description: "Pouzdana rješenja za vašu dokumentaciju i administrativne potrebe",
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '32x32', type: 'image/x-icon' },
      { url: '/icon1.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { url: '/icon0.svg', type: 'image/svg+xml' },
    ],
  },
  manifest: '/manifest.json',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="bs" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Check if we're in production (not localhost)
              const isProduction = !window.location.hostname.includes('localhost') &&
                                   !window.location.hostname.includes('127.0.0.1');

              // Only register service worker in production
              if ('serviceWorker' in navigator && isProduction) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }

              // BFCache optimization
              window.addEventListener('DOMContentLoaded', function() {
                // Remove any beforeunload/unload listeners that might prevent bfcache
                window.removeEventListener('beforeunload', function() {});
                window.removeEventListener('unload', function() {});

                // Log bfcache events
                window.addEventListener('pageshow', function(event) {
                  if (event.persisted) {
                    console.log('Page restored from bfcache');
                  }
                });

                window.addEventListener('pagehide', function(event) {
                  if (event.persisted) {
                    console.log('Page stored in bfcache');
                  } else if (isProduction) {
                    // Only log bfcache issues in production
                    console.log('Page not eligible for bfcache');
                  }
                });
              });
            `,
          }}
        />
        <meta name="apple-mobile-web-app-title" content="AlbatrosDoc" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="theme-color" content="#f5fbef" />
        <meta name="msapplication-TileColor" content="#f5fbef" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
      </head>
      <body
        className={`${metroSans.variable} antialiased font-metro-sans`}
        suppressHydrationWarning
      >
        {children}
      </body>
    </html>
  );
}
