import type { Metadata } from 'next';
import { getHtmlLang } from '@/lib/locale';

// Use environment-aware base URL
const getBaseUrl = () => {
  if (process.env.NODE_ENV === 'development') {
    return process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  }
  return process.env.NEXT_PUBLIC_BASE_URL || 'https://albatrosdoc.com';
};

const baseUrl = getBaseUrl();

interface PageMetadata {
  title: string;
  description: string;
  keywords?: string;
}

const metadataByPage: Record<string, Record<string, PageMetadata>> = {
  home: {
    'bs': {
      title: 'AlbatrosDoc - Pouzdana rješenja za vašu dokumentaciju',
      description: 'Pouzdana rješenja za vašu dokumentaciju i administrativne potrebe. Brza, sigurna i profesionalna usluga nabavke i dostave dokumenata u Sarajevu.',
      keywords: 'dokumentacija, administrativne usluge, nabavka dokumenata, dostava dokumenata, Sarajevo, BiH, albatrosdoc'
    },
    'en': {
      title: 'AlbatrosDoc - Reliable Solutions for Your Documentation',
      description: 'Reliable solutions for your documentation and administrative needs. Fast, secure and professional document procurement and delivery services in Sarajevo.',
      keywords: 'documentation, administrative services, document procurement, document delivery, Sarajevo, Bosnia, albatrosdoc'
    },
    'de': {
      title: 'AlbatrosDoc - Zuverlässige Lösungen für Ihre Dokumentation',
      description: 'Zuverlässige Lösungen für Ihre Dokumentations- und Verwaltungsbedürfnisse. Schnelle, sichere und professionelle Dokumentenbeschaffung und -zustellung in Sarajevo.',
      keywords: 'Dokumentation, Verwaltungsdienstleistungen, Dokumentenbeschaffung, Dokumentenzustellung, Sarajevo, Bosnien, albatrosdoc'
    }
  },
  about: {
    'bs': {
      title: 'O nama - AlbatrosDoc | Vaš pouzdani partner za dokumentaciju',
      description: 'Saznajte više o AlbatrosDoc timu i našoj misiji pružanja vrhunskih usluga dokumentacije i administrativnih potreba u Sarajevu.',
      keywords: 'o nama, albatrosdoc tim, misija, vizija, dokumentacija, administrativne usluge, Sarajevo'
    },
    'en': {
      title: 'About Us - AlbatrosDoc | Your Trusted Documentation Partner',
      description: 'Learn more about the AlbatrosDoc team and our mission to provide excellent documentation and administrative services in Sarajevo.',
      keywords: 'about us, albatrosdoc team, mission, vision, documentation, administrative services, Sarajevo'
    },
    'de': {
      title: 'Über uns - AlbatrosDoc | Ihr vertrauensvoller Dokumentationspartner',
      description: 'Erfahren Sie mehr über das AlbatrosDoc-Team und unsere Mission, exzellente Dokumentations- und Verwaltungsdienstleistungen in Sarajevo anzubieten.',
      keywords: 'über uns, albatrosdoc team, mission, vision, dokumentation, verwaltungsdienstleistungen, Sarajevo'
    }
  },
  services: {
    'bs': {
      title: 'Usluge - AlbatrosDoc | Kompletne administrativne usluge',
      description: 'Otkrijte naš širok spektar usluga: nabavka dokumenata, administrativne usluge, dostava i još mnogo toga. Brzo, sigurno i profesionalno.',
      keywords: 'usluge, nabavka dokumenata, administrativne usluge, dostava, prevod dokumenata, ovjeravanje, Sarajevo'
    },
    'en': {
      title: 'Services - AlbatrosDoc | Complete Administrative Services',
      description: 'Discover our wide range of services: document procurement, administrative services, delivery and much more. Fast, secure and professional.',
      keywords: 'services, document procurement, administrative services, delivery, document translation, notarization, Sarajevo'
    },
    'de': {
      title: 'Dienstleistungen - AlbatrosDoc | Komplette Verwaltungsdienstleistungen',
      description: 'Entdecken Sie unser breites Spektrum an Dienstleistungen: Dokumentenbeschaffung, Verwaltungsdienstleistungen, Zustellung und vieles mehr. Schnell, sicher und professionell.',
      keywords: 'dienstleistungen, dokumentenbeschaffung, verwaltungsdienstleistungen, zustellung, dokumentenübersetzung, beglaubigung, Sarajevo'
    }
  },
  contact: {
    'bs': {
      title: 'Kontakt - AlbatrosDoc | Stupite u kontakt s nama',
      description: 'Kontaktirajte AlbatrosDoc za sve vaše potrebe dokumentacije. Dostupni smo u Sarajevu za brzu i profesionalnu uslugu.',
      keywords: 'kontakt, albatrosdoc, Sarajevo, telefon, email, adresa, dokumentacija, administrativne usluge'
    },
    'en': {
      title: 'Contact - AlbatrosDoc | Get in Touch with Us',
      description: 'Contact AlbatrosDoc for all your documentation needs. We are available in Sarajevo for fast and professional service.',
      keywords: 'contact, albatrosdoc, Sarajevo, phone, email, address, documentation, administrative services'
    },
    'de': {
      title: 'Kontakt - AlbatrosDoc | Nehmen Sie Kontakt mit uns auf',
      description: 'Kontaktieren Sie AlbatrosDoc für alle Ihre Dokumentationsbedürfnisse. Wir sind in Sarajevo für schnellen und professionellen Service verfügbar.',
      keywords: 'kontakt, albatrosdoc, Sarajevo, telefon, email, adresse, dokumentation, verwaltungsdienstleistungen'
    }
  }
};

export function generatePageMetadata(
  page: keyof typeof metadataByPage,
  locale: string,
  pathname?: string
): Metadata {
  const pageData = metadataByPage[page]?.[locale] || metadataByPage[page]?.['bs'];

  if (!pageData) {
    // Fallback to home page metadata
    const fallback = metadataByPage.home[locale] || metadataByPage.home['bs'];
    return generateMetadata(fallback, locale, pathname, page);
  }

  return generateMetadata(pageData, locale, pathname, page);
}

function generateMetadata(data: PageMetadata, locale: string, pathname?: string, page?: keyof typeof metadataByPage): Metadata {
  const url = pathname ? `${baseUrl}${pathname}` : baseUrl;

  // Generate language alternates based on the page
  const getLanguageAlternates = () => {
    if (!page || page === 'home') {
      return {
        'bs-BA': `${baseUrl}/`,
        'en': `${baseUrl}/en`,
        'de': `${baseUrl}/de`,
        'x-default': `${baseUrl}/`,
      };
    }

    const pathMap = {
      about: {
        'bs-BA': `${baseUrl}/o-nama`,
        'en': `${baseUrl}/en/about`,
        'de': `${baseUrl}/de/uber-uns`,
        'x-default': `${baseUrl}/o-nama`,
      },
      services: {
        'bs-BA': `${baseUrl}/usluge`,
        'en': `${baseUrl}/en/services`,
        'de': `${baseUrl}/de/dienstleistungen`,
        'x-default': `${baseUrl}/usluge`,
      },
      contact: {
        'bs-BA': `${baseUrl}/kontakt`,
        'en': `${baseUrl}/en/contact`,
        'de': `${baseUrl}/de/kontakt`,
        'x-default': `${baseUrl}/kontakt`,
      },
    };

    return pathMap[page] || {
      'bs-BA': `${baseUrl}/`,
      'en': `${baseUrl}/en`,
      'de': `${baseUrl}/de`,
      'x-default': `${baseUrl}/`,
    };
  };
  
  return {
    title: data.title,
    description: data.description,
    keywords: data.keywords,
    authors: [{ name: 'AlbatrosDoc' }],
    creator: 'AlbatrosDoc',
    publisher: 'AlbatrosDoc',
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      title: data.title,
      description: data.description,
      url,
      siteName: 'AlbatrosDoc',
      images: [
        {
          url: `${baseUrl}/images/og-image.png`,
          width: 1200,
          height: 630,
          alt: data.title,
        },
      ],
      locale: getHtmlLang(locale),
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: data.title,
      description: data.description,
      images: [`${baseUrl}/images/og-image.png`],
      creator: '@albatrosdoc', // Update if you have a Twitter handle
    },
    alternates: {
      canonical: url,
      languages: getLanguageAlternates(),
    },
  };
}
