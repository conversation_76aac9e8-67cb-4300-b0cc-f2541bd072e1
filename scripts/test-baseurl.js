#!/usr/bin/env node

// Test the baseUrl detection logic
function getBaseUrl() {
  // Check for explicit environment variable first
  if (process.env.NEXT_PUBLIC_BASE_URL) {
    return process.env.NEXT_PUBLIC_BASE_URL;
  }
  
  // Check for Vercel deployment
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  
  // Development environment
  if (process.env.NODE_ENV === 'development') {
    return 'http://localhost:3000';
  }
  
  // Production fallback
  return 'https://albatrosdoc.com';
}

console.log('🔍 Testing Base URL Detection\n');

console.log('Current Environment Variables:');
console.log('─'.repeat(40));
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
console.log(`NEXT_PUBLIC_BASE_URL: ${process.env.NEXT_PUBLIC_BASE_URL || 'undefined'}`);
console.log(`VERCEL_URL: ${process.env.VERCEL_URL || 'undefined'}`);

console.log('\nDetected Base URL:');
console.log('─'.repeat(40));
console.log(`✅ ${getBaseUrl()}`);

console.log('\nTest Scenarios:');
console.log('─'.repeat(40));

// Test development
const originalNodeEnv = process.env.NODE_ENV;
const originalBaseUrl = process.env.NEXT_PUBLIC_BASE_URL;
const originalVercelUrl = process.env.VERCEL_URL;

// Scenario 1: Development
delete process.env.NEXT_PUBLIC_BASE_URL;
delete process.env.VERCEL_URL;
process.env.NODE_ENV = 'development';
console.log(`Development: ${getBaseUrl()}`);

// Scenario 2: Vercel deployment
process.env.NODE_ENV = 'production';
process.env.VERCEL_URL = 'albatrosdoc.vercel.app';
console.log(`Vercel: ${getBaseUrl()}`);

// Scenario 3: Custom domain
process.env.NEXT_PUBLIC_BASE_URL = 'https://albatrosdoc.com';
console.log(`Custom Domain: ${getBaseUrl()}`);

// Scenario 4: Production fallback
delete process.env.NEXT_PUBLIC_BASE_URL;
delete process.env.VERCEL_URL;
process.env.NODE_ENV = 'production';
console.log(`Production Fallback: ${getBaseUrl()}`);

// Restore original values
process.env.NODE_ENV = originalNodeEnv;
if (originalBaseUrl) process.env.NEXT_PUBLIC_BASE_URL = originalBaseUrl;
if (originalVercelUrl) process.env.VERCEL_URL = originalVercelUrl;

console.log('\n📋 Priority Order:');
console.log('─'.repeat(40));
console.log('1. NEXT_PUBLIC_BASE_URL (explicit override)');
console.log('2. VERCEL_URL (automatic Vercel detection)');
console.log('3. NODE_ENV=development → localhost:3000');
console.log('4. Production fallback → albatrosdoc.com');
