#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const http = require('http');

const port = process.argv[2] || 3000;
const baseUrl = `http://localhost:${port}`;

// List of favicon files to check
const faviconFiles = [
  '/favicon.ico',
  '/apple-icon.png',
  '/icon0.svg',
  '/icon1.png',
  '/web-app-manifest-192x192.png',
  '/web-app-manifest-512x512.png',
  '/manifest.json'
];

console.log(`🔍 Checking favicons on ${baseUrl}...\n`);

// Function to check if a URL returns 200
function checkUrl(url) {
  return new Promise((resolve) => {
    const req = http.get(url, (res) => {
      resolve({
        url,
        status: res.statusCode,
        contentType: res.headers['content-type'],
        contentLength: res.headers['content-length']
      });
    });
    
    req.on('error', (err) => {
      resolve({
        url,
        status: 'ERROR',
        error: err.message
      });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        url,
        status: 'TIMEOUT'
      });
    });
  });
}

// Function to check if files exist in filesystem
function checkFileExists(filePath) {
  const fullPath = path.join(process.cwd(), 'src/app', filePath);
  const publicPath = path.join(process.cwd(), 'public', filePath);
  
  if (fs.existsSync(fullPath)) {
    const stats = fs.statSync(fullPath);
    return { exists: true, location: 'src/app', size: stats.size };
  } else if (fs.existsSync(publicPath)) {
    const stats = fs.statSync(publicPath);
    return { exists: true, location: 'public', size: stats.size };
  }
  
  return { exists: false };
}

async function main() {
  console.log('📁 File System Check:');
  console.log('─'.repeat(50));
  
  for (const file of faviconFiles) {
    const fileCheck = checkFileExists(file);
    if (fileCheck.exists) {
      console.log(`✅ ${file} - Found in ${fileCheck.location}/ (${fileCheck.size} bytes)`);
    } else {
      console.log(`❌ ${file} - Not found`);
    }
  }
  
  console.log('\n🌐 HTTP Response Check:');
  console.log('─'.repeat(50));
  
  const results = await Promise.all(
    faviconFiles.map(file => checkUrl(`${baseUrl}${file}`))
  );
  
  for (const result of results) {
    if (result.status === 200) {
      console.log(`✅ ${result.url} - ${result.status} (${result.contentType}, ${result.contentLength} bytes)`);
    } else if (result.status === 'ERROR') {
      console.log(`❌ ${result.url} - ${result.error}`);
    } else {
      console.log(`⚠️  ${result.url} - ${result.status}`);
    }
  }
  
  // Check main page for favicon meta tags
  console.log('\n🏷️  HTML Meta Tags Check:');
  console.log('─'.repeat(50));
  
  try {
    const pageResult = await checkUrl(baseUrl);
    if (pageResult.status === 200) {
      console.log('✅ Main page accessible');
      console.log('💡 Check browser dev tools for favicon meta tags in <head>');
    } else {
      console.log(`❌ Main page not accessible: ${pageResult.status}`);
    }
  } catch (error) {
    console.log(`❌ Error checking main page: ${error.message}`);
  }
  
  console.log('\n📋 Summary:');
  console.log('─'.repeat(50));
  console.log('• Make sure your dev server is running on port', port);
  console.log('• Check browser dev tools Network tab for 404s');
  console.log('• Verify favicon appears in browser tab');
  console.log('• Test on different devices/browsers');
}

main().catch(console.error);
